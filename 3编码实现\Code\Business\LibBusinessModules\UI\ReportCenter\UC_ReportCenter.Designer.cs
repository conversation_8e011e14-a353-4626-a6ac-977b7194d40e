namespace LibBusinessModules.ReportCenter.UI
{
    partial class UC_ReportCenter
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle9 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle10 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle11 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle12 = new System.Windows.Forms.DataGridViewCellStyle();
            this.saveFileDialog = new System.Windows.Forms.SaveFileDialog();
            this.pnlTitle = new Sunny.UI.UIPanel();
            this.dtpMonth = new Sunny.UI.UIDatePicker();
            this.dtpYear = new Sunny.UI.UIDatePicker();
            this.btnExportPNG = new Sunny.UI.UIButton();
            this.lblCurrentPeriod = new Sunny.UI.UILabel();
            this.pnlDateSelector = new Sunny.UI.UIPanel();
            this.nudWeekNumber = new Sunny.UI.UIIntegerUpDown();
            this.lblWeekYear = new Sunny.UI.UILabel();
            this.lblWeekText = new Sunny.UI.UILabel();
            this.lblDayText = new Sunny.UI.UILabel();
            this.btnQuery = new Sunny.UI.UIButton();
            this.pnlPeriodType = new Sunny.UI.UIPanel();
            this.rbYear = new Sunny.UI.UIRadioButton();
            this.rbMonth = new Sunny.UI.UIRadioButton();
            this.rbWeek = new Sunny.UI.UIRadioButton();
            this.rbDay = new Sunny.UI.UIRadioButton();
            this.uiLabel3 = new Sunny.UI.UILabel();
            this.pnlMain = new Sunny.UI.UIPanel();
            this.pnlDataTable = new Sunny.UI.UIPanel();
            this.dgvStatistics = new Sunny.UI.UIDataGridView();
            this.lblDataTableTitle = new Sunny.UI.UILabel();
            this.trendChart = new LiveChartsCore.SkiaSharpView.WinForms.CartesianChart();
            this.dtpEndTime = new Sunny.UI.UIDatePicker();
            this.dtpStartTime = new Sunny.UI.UIDatePicker();
            this.pnlTitle.SuspendLayout();
            this.pnlDateSelector.SuspendLayout();
            this.pnlPeriodType.SuspendLayout();
            this.pnlMain.SuspendLayout();
            this.pnlDataTable.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvStatistics)).BeginInit();
            this.SuspendLayout();
            // 
            // saveFileDialog
            // 
            this.saveFileDialog.DefaultExt = "docx";
            this.saveFileDialog.Filter = "Word文档|*.docx";
            this.saveFileDialog.Title = "导出设备趋势统计";
            // 
            // pnlTitle
            // 
            this.pnlTitle.Controls.Add(this.pnlDateSelector);
            this.pnlTitle.Controls.Add(this.btnQuery);
            this.pnlTitle.Controls.Add(this.pnlPeriodType);
            this.pnlTitle.Controls.Add(this.lblCurrentPeriod);
            this.pnlTitle.Controls.Add(this.uiLabel3);
            this.pnlTitle.Controls.Add(this.btnExportPNG);
            this.pnlTitle.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlTitle.Font = new System.Drawing.Font("宋体", 12F);
            this.pnlTitle.Location = new System.Drawing.Point(0, 0);
            this.pnlTitle.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlTitle.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlTitle.Name = "pnlTitle";
            this.pnlTitle.Size = new System.Drawing.Size(1200, 101);
            this.pnlTitle.Style = Sunny.UI.UIStyle.Custom;
            this.pnlTitle.TabIndex = 0;
            this.pnlTitle.Text = null;
            this.pnlTitle.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // dtpMonth
            // 
            this.dtpMonth.DateYearFormat = "yyyy年";
            this.dtpMonth.DateYearMonthFormat = "yyyy年MM月";
            this.dtpMonth.FillColor = System.Drawing.Color.White;
            this.dtpMonth.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.dtpMonth.Location = new System.Drawing.Point(4, 6);
            this.dtpMonth.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.dtpMonth.MaxLength = 8;
            this.dtpMonth.MinimumSize = new System.Drawing.Size(63, 0);
            this.dtpMonth.Name = "dtpMonth";
            this.dtpMonth.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.dtpMonth.ShowType = Sunny.UI.UIDateType.YearMonth;
            this.dtpMonth.Size = new System.Drawing.Size(111, 34);
            this.dtpMonth.SymbolDropDown = 61555;
            this.dtpMonth.SymbolNormal = 61555;
            this.dtpMonth.SymbolSize = 24;
            this.dtpMonth.TabIndex = 28;
            this.dtpMonth.Text = "2025年01月";
            this.dtpMonth.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.dtpMonth.Value = new System.DateTime(2025, 1, 1, 0, 0, 0, 0);
            this.dtpMonth.Watermark = "";
            // 
            // dtpYear
            // 
            this.dtpYear.DateYearFormat = "yyyy年";
            this.dtpYear.FillColor = System.Drawing.Color.White;
            this.dtpYear.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.dtpYear.Location = new System.Drawing.Point(4, 6);
            this.dtpYear.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.dtpYear.MaxLength = 5;
            this.dtpYear.MinimumSize = new System.Drawing.Size(63, 0);
            this.dtpYear.Name = "dtpYear";
            this.dtpYear.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.dtpYear.ShowType = Sunny.UI.UIDateType.Year;
            this.dtpYear.Size = new System.Drawing.Size(85, 34);
            this.dtpYear.SymbolDropDown = 61555;
            this.dtpYear.SymbolNormal = 61555;
            this.dtpYear.SymbolSize = 24;
            this.dtpYear.TabIndex = 27;
            this.dtpYear.Text = "2025年";
            this.dtpYear.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.dtpYear.Value = new System.DateTime(2025, 1, 1, 0, 0, 0, 0);
            this.dtpYear.Watermark = "";
            // 
            // btnExportPNG
            // 
            this.btnExportPNG.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnExportPNG.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnExportPNG.Font = new System.Drawing.Font("宋体", 12F);
            this.btnExportPNG.Location = new System.Drawing.Point(1093, 16);
            this.btnExportPNG.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnExportPNG.Name = "btnExportPNG";
            this.btnExportPNG.Size = new System.Drawing.Size(92, 32);
            this.btnExportPNG.Style = Sunny.UI.UIStyle.Custom;
            this.btnExportPNG.TabIndex = 26;
            this.btnExportPNG.Text = "导出Word";
            this.btnExportPNG.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnExportPNG.Click += new System.EventHandler(this.btnExportPNG_Click);
            // 
            // lblCurrentPeriod
            // 
            this.lblCurrentPeriod.AutoSize = true;
            this.lblCurrentPeriod.BackColor = System.Drawing.Color.Transparent;
            this.lblCurrentPeriod.Font = new System.Drawing.Font("宋体", 12F);
            this.lblCurrentPeriod.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblCurrentPeriod.Location = new System.Drawing.Point(10, 70);
            this.lblCurrentPeriod.Name = "lblCurrentPeriod";
            this.lblCurrentPeriod.Size = new System.Drawing.Size(119, 16);
            this.lblCurrentPeriod.Style = Sunny.UI.UIStyle.Custom;
            this.lblCurrentPeriod.TabIndex = 25;
            this.lblCurrentPeriod.Text = "当前统计周期：";
            this.lblCurrentPeriod.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // pnlDateSelector
            // 
            this.pnlDateSelector.Controls.Add(this.dtpMonth);
            this.pnlDateSelector.Controls.Add(this.dtpEndTime);
            this.pnlDateSelector.Controls.Add(this.dtpYear);
            this.pnlDateSelector.Controls.Add(this.nudWeekNumber);
            this.pnlDateSelector.Controls.Add(this.lblWeekYear);
            this.pnlDateSelector.Controls.Add(this.lblWeekText);
            this.pnlDateSelector.Controls.Add(this.lblDayText);
            this.pnlDateSelector.Controls.Add(this.dtpStartTime);
            this.pnlDateSelector.Font = new System.Drawing.Font("宋体", 12F);
            this.pnlDateSelector.Location = new System.Drawing.Point(300, 9);
            this.pnlDateSelector.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlDateSelector.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlDateSelector.Name = "pnlDateSelector";
            this.pnlDateSelector.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.pnlDateSelector.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.pnlDateSelector.Size = new System.Drawing.Size(400, 46);
            this.pnlDateSelector.Style = Sunny.UI.UIStyle.Custom;
            this.pnlDateSelector.TabIndex = 24;
            this.pnlDateSelector.Text = null;
            this.pnlDateSelector.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // nudWeekNumber
            // 
            this.nudWeekNumber.Font = new System.Drawing.Font("宋体", 12F);
            this.nudWeekNumber.Location = new System.Drawing.Point(120, 6);
            this.nudWeekNumber.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.nudWeekNumber.Maximum = 53;
            this.nudWeekNumber.Minimum = 1;
            this.nudWeekNumber.MinimumSize = new System.Drawing.Size(63, 34);
            this.nudWeekNumber.Name = "nudWeekNumber";
            this.nudWeekNumber.ShowText = false;
            this.nudWeekNumber.Size = new System.Drawing.Size(84, 34);
            this.nudWeekNumber.Style = Sunny.UI.UIStyle.Custom;
            this.nudWeekNumber.TabIndex = 23;
            this.nudWeekNumber.Text = "1";
            this.nudWeekNumber.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            this.nudWeekNumber.Value = 1;
            this.nudWeekNumber.Visible = false;
            // 
            // lblWeekYear
            // 
            this.lblWeekYear.AutoSize = true;
            this.lblWeekYear.BackColor = System.Drawing.Color.Transparent;
            this.lblWeekYear.Font = new System.Drawing.Font("宋体", 12F);
            this.lblWeekYear.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblWeekYear.Location = new System.Drawing.Point(95, 15);
            this.lblWeekYear.Name = "lblWeekYear";
            this.lblWeekYear.Size = new System.Drawing.Size(23, 16);
            this.lblWeekYear.Style = Sunny.UI.UIStyle.Custom;
            this.lblWeekYear.TabIndex = 24;
            this.lblWeekYear.Text = "第";
            this.lblWeekYear.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lblWeekYear.Visible = false;
            // 
            // lblWeekText
            // 
            this.lblWeekText.AutoSize = true;
            this.lblWeekText.BackColor = System.Drawing.Color.Transparent;
            this.lblWeekText.Font = new System.Drawing.Font("宋体", 12F);
            this.lblWeekText.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblWeekText.Location = new System.Drawing.Point(209, 15);
            this.lblWeekText.Name = "lblWeekText";
            this.lblWeekText.Size = new System.Drawing.Size(23, 16);
            this.lblWeekText.Style = Sunny.UI.UIStyle.Custom;
            this.lblWeekText.TabIndex = 25;
            this.lblWeekText.Text = "周";
            this.lblWeekText.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lblWeekText.Visible = false;
            // 
            // lblDayText
            // 
            this.lblDayText.AutoSize = true;
            this.lblDayText.BackColor = System.Drawing.Color.Transparent;
            this.lblDayText.Font = new System.Drawing.Font("宋体", 12F);
            this.lblDayText.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblDayText.Location = new System.Drawing.Point(161, 15);
            this.lblDayText.Name = "lblDayText";
            this.lblDayText.Size = new System.Drawing.Size(23, 16);
            this.lblDayText.Style = Sunny.UI.UIStyle.Custom;
            this.lblDayText.TabIndex = 16;
            this.lblDayText.Text = "至";
            this.lblDayText.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnQuery
            // 
            this.btnQuery.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnQuery.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnQuery.Font = new System.Drawing.Font("宋体", 12F);
            this.btnQuery.Location = new System.Drawing.Point(995, 16);
            this.btnQuery.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnQuery.Name = "btnQuery";
            this.btnQuery.Size = new System.Drawing.Size(92, 32);
            this.btnQuery.Style = Sunny.UI.UIStyle.Custom;
            this.btnQuery.TabIndex = 23;
            this.btnQuery.Text = "开始统计";
            this.btnQuery.TipsFont = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnQuery.Click += new System.EventHandler(this.btnQuery_Click);
            // 
            // pnlPeriodType
            // 
            this.pnlPeriodType.Controls.Add(this.rbYear);
            this.pnlPeriodType.Controls.Add(this.rbMonth);
            this.pnlPeriodType.Controls.Add(this.rbWeek);
            this.pnlPeriodType.Controls.Add(this.rbDay);
            this.pnlPeriodType.Font = new System.Drawing.Font("宋体", 12F);
            this.pnlPeriodType.Location = new System.Drawing.Point(98, 9);
            this.pnlPeriodType.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlPeriodType.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlPeriodType.Name = "pnlPeriodType";
            this.pnlPeriodType.Size = new System.Drawing.Size(200, 46);
            this.pnlPeriodType.Style = Sunny.UI.UIStyle.Custom;
            this.pnlPeriodType.TabIndex = 15;
            this.pnlPeriodType.Text = null;
            this.pnlPeriodType.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rbYear
            // 
            this.rbYear.BackColor = System.Drawing.Color.Transparent;
            this.rbYear.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rbYear.Font = new System.Drawing.Font("宋体", 12F);
            this.rbYear.Location = new System.Drawing.Point(3, 12);
            this.rbYear.MinimumSize = new System.Drawing.Size(1, 1);
            this.rbYear.Name = "rbYear";
            this.rbYear.Size = new System.Drawing.Size(40, 22);
            this.rbYear.Style = Sunny.UI.UIStyle.Custom;
            this.rbYear.TabIndex = 10;
            this.rbYear.Text = "年";
            this.rbYear.CheckedChanged += new System.EventHandler(this.rbPeriodType_CheckedChanged);
            // 
            // rbMonth
            // 
            this.rbMonth.BackColor = System.Drawing.Color.Transparent;
            this.rbMonth.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rbMonth.Font = new System.Drawing.Font("宋体", 12F);
            this.rbMonth.Location = new System.Drawing.Point(50, 12);
            this.rbMonth.MinimumSize = new System.Drawing.Size(1, 1);
            this.rbMonth.Name = "rbMonth";
            this.rbMonth.Size = new System.Drawing.Size(40, 22);
            this.rbMonth.Style = Sunny.UI.UIStyle.Custom;
            this.rbMonth.TabIndex = 11;
            this.rbMonth.Text = "月";
            this.rbMonth.CheckedChanged += new System.EventHandler(this.rbPeriodType_CheckedChanged);
            // 
            // rbWeek
            // 
            this.rbWeek.BackColor = System.Drawing.Color.Transparent;
            this.rbWeek.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rbWeek.Font = new System.Drawing.Font("宋体", 12F);
            this.rbWeek.Location = new System.Drawing.Point(97, 12);
            this.rbWeek.MinimumSize = new System.Drawing.Size(1, 1);
            this.rbWeek.Name = "rbWeek";
            this.rbWeek.Size = new System.Drawing.Size(40, 22);
            this.rbWeek.Style = Sunny.UI.UIStyle.Custom;
            this.rbWeek.TabIndex = 12;
            this.rbWeek.Text = "周";
            this.rbWeek.CheckedChanged += new System.EventHandler(this.rbPeriodType_CheckedChanged);
            // 
            // rbDay
            // 
            this.rbDay.BackColor = System.Drawing.Color.Transparent;
            this.rbDay.Checked = true;
            this.rbDay.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rbDay.Font = new System.Drawing.Font("宋体", 12F);
            this.rbDay.Location = new System.Drawing.Point(144, 12);
            this.rbDay.MinimumSize = new System.Drawing.Size(1, 1);
            this.rbDay.Name = "rbDay";
            this.rbDay.Size = new System.Drawing.Size(40, 22);
            this.rbDay.Style = Sunny.UI.UIStyle.Custom;
            this.rbDay.TabIndex = 13;
            this.rbDay.Text = "日";
            this.rbDay.CheckedChanged += new System.EventHandler(this.rbPeriodType_CheckedChanged);
            // 
            // uiLabel3
            // 
            this.uiLabel3.AutoSize = true;
            this.uiLabel3.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel3.Font = new System.Drawing.Font("宋体", 12F);
            this.uiLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel3.Location = new System.Drawing.Point(8, 24);
            this.uiLabel3.Name = "uiLabel3";
            this.uiLabel3.Size = new System.Drawing.Size(87, 16);
            this.uiLabel3.Style = Sunny.UI.UIStyle.Custom;
            this.uiLabel3.TabIndex = 14;
            this.uiLabel3.Text = "统计周期：";
            this.uiLabel3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // pnlMain
            // 
            this.pnlMain.Controls.Add(this.trendChart);
            this.pnlMain.Controls.Add(this.pnlDataTable);
            this.pnlMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlMain.Font = new System.Drawing.Font("宋体", 12F);
            this.pnlMain.Location = new System.Drawing.Point(0, 101);
            this.pnlMain.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlMain.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.Size = new System.Drawing.Size(1200, 599);
            this.pnlMain.Style = Sunny.UI.UIStyle.Custom;
            this.pnlMain.TabIndex = 1;
            this.pnlMain.Text = null;
            this.pnlMain.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // pnlDataTable
            // 
            this.pnlDataTable.Controls.Add(this.dgvStatistics);
            this.pnlDataTable.Controls.Add(this.lblDataTableTitle);
            this.pnlDataTable.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlDataTable.Font = new System.Drawing.Font("宋体", 12F);
            this.pnlDataTable.Location = new System.Drawing.Point(0, 0);
            this.pnlDataTable.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.pnlDataTable.MinimumSize = new System.Drawing.Size(1, 1);
            this.pnlDataTable.Name = "pnlDataTable";
            this.pnlDataTable.Size = new System.Drawing.Size(1200, 200);
            this.pnlDataTable.Style = Sunny.UI.UIStyle.Custom;
            this.pnlDataTable.TabIndex = 1;
            this.pnlDataTable.Text = null;
            this.pnlDataTable.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // dgvStatistics
            // 
            this.dgvStatistics.AllowUserToAddRows = false;
            this.dgvStatistics.AllowUserToDeleteRows = false;
            this.dgvStatistics.AllowUserToResizeRows = false;
            dataGridViewCellStyle9.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.dgvStatistics.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle9;
            this.dgvStatistics.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgvStatistics.BackgroundColor = System.Drawing.Color.White;
            this.dgvStatistics.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle10.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle10.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle10.Font = new System.Drawing.Font("宋体", 12F);
            dataGridViewCellStyle10.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle10.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle10.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle10.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvStatistics.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle10;
            this.dgvStatistics.ColumnHeadersHeight = 32;
            this.dgvStatistics.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgvStatistics.EnableHeadersVisualStyles = false;
            this.dgvStatistics.Font = new System.Drawing.Font("宋体", 12F);
            this.dgvStatistics.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            this.dgvStatistics.Location = new System.Drawing.Point(10, 35);
            this.dgvStatistics.Name = "dgvStatistics";
            this.dgvStatistics.ReadOnly = true;
            dataGridViewCellStyle11.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle11.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle11.Font = new System.Drawing.Font("宋体", 12F);
            dataGridViewCellStyle11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle11.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle11.SelectionForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle11.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvStatistics.RowHeadersDefaultCellStyle = dataGridViewCellStyle11;
            this.dgvStatistics.RowHeadersVisible = false;
            dataGridViewCellStyle12.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle12.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.dgvStatistics.RowsDefaultCellStyle = dataGridViewCellStyle12;
            this.dgvStatistics.RowTemplate.Height = 29;
            this.dgvStatistics.SelectedIndex = -1;
            this.dgvStatistics.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvStatistics.Size = new System.Drawing.Size(1180, 155);
            this.dgvStatistics.StripeOddColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.dgvStatistics.Style = Sunny.UI.UIStyle.Custom;
            this.dgvStatistics.TabIndex = 1;
            // 
            // lblDataTableTitle
            // 
            this.lblDataTableTitle.AutoSize = true;
            this.lblDataTableTitle.BackColor = System.Drawing.Color.Transparent;
            this.lblDataTableTitle.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold);
            this.lblDataTableTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblDataTableTitle.Location = new System.Drawing.Point(10, 10);
            this.lblDataTableTitle.Name = "lblDataTableTitle";
            this.lblDataTableTitle.Size = new System.Drawing.Size(92, 16);
            this.lblDataTableTitle.Style = Sunny.UI.UIStyle.Custom;
            this.lblDataTableTitle.TabIndex = 0;
            this.lblDataTableTitle.Text = "统计数据表";
            this.lblDataTableTitle.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // trendChart
            // 
            this.trendChart.Dock = System.Windows.Forms.DockStyle.Fill;
            this.trendChart.Location = new System.Drawing.Point(0, 200);
            this.trendChart.Margin = new System.Windows.Forms.Padding(7, 7, 7, 7);
            this.trendChart.MatchAxesScreenDataRatio = false;
            this.trendChart.Name = "trendChart";
            this.trendChart.Size = new System.Drawing.Size(1200, 399);
            this.trendChart.TabIndex = 0;
            // 
            // dtpEndTime
            // 
            this.dtpEndTime.DateFormat = "yyyy年MM月dd日";
            this.dtpEndTime.DateYearFormat = "yyyy年";
            this.dtpEndTime.DateYearMonthFormat = "yyyy年MM月";
            this.dtpEndTime.FillColor = System.Drawing.Color.White;
            this.dtpEndTime.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.dtpEndTime.Location = new System.Drawing.Point(195, 6);
            this.dtpEndTime.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.dtpEndTime.MaxLength = 11;
            this.dtpEndTime.MinimumSize = new System.Drawing.Size(63, 0);
            this.dtpEndTime.Name = "dtpEndTime";
            this.dtpEndTime.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.dtpEndTime.Size = new System.Drawing.Size(146, 34);
            this.dtpEndTime.SymbolDropDown = 61555;
            this.dtpEndTime.SymbolNormal = 61555;
            this.dtpEndTime.SymbolSize = 24;
            this.dtpEndTime.TabIndex = 29;
            this.dtpEndTime.Text = "2025年01月01日";
            this.dtpEndTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.dtpEndTime.Value = new System.DateTime(2025, 1, 1, 0, 0, 0, 0);
            this.dtpEndTime.Watermark = "";
            // 
            // dtpStartTime
            // 
            this.dtpStartTime.DateFormat = "yyyy年MM月dd日";
            this.dtpStartTime.DateYearFormat = "yyyy年";
            this.dtpStartTime.DateYearMonthFormat = "yyyy年MM月";
            this.dtpStartTime.FillColor = System.Drawing.Color.White;
            this.dtpStartTime.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.dtpStartTime.Location = new System.Drawing.Point(8, 6);
            this.dtpStartTime.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.dtpStartTime.MaxLength = 11;
            this.dtpStartTime.MinimumSize = new System.Drawing.Size(63, 0);
            this.dtpStartTime.Name = "dtpStartTime";
            this.dtpStartTime.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.dtpStartTime.Size = new System.Drawing.Size(146, 34);
            this.dtpStartTime.SymbolDropDown = 61555;
            this.dtpStartTime.SymbolNormal = 61555;
            this.dtpStartTime.SymbolSize = 24;
            this.dtpStartTime.TabIndex = 30;
            this.dtpStartTime.Text = "2025年01月01日";
            this.dtpStartTime.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.dtpStartTime.Value = new System.DateTime(2025, 1, 1, 0, 0, 0, 0);
            this.dtpStartTime.Watermark = "";
            // 
            // UC_ReportCenter
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.pnlMain);
            this.Controls.Add(this.pnlTitle);
            this.Name = "UC_ReportCenter";
            this.Size = new System.Drawing.Size(1200, 700);
            this.Load += new System.EventHandler(this.UC_DeviceTrendStatistics_Load);
            this.pnlTitle.ResumeLayout(false);
            this.pnlTitle.PerformLayout();
            this.pnlDateSelector.ResumeLayout(false);
            this.pnlDateSelector.PerformLayout();
            this.pnlPeriodType.ResumeLayout(false);
            this.pnlMain.ResumeLayout(false);
            this.pnlDataTable.ResumeLayout(false);
            this.pnlDataTable.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvStatistics)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.SaveFileDialog saveFileDialog;
        protected Sunny.UI.UIPanel pnlTitle;
        private Sunny.UI.UIButton btnExportPNG;
        private Sunny.UI.UILabel lblCurrentPeriod;
        private Sunny.UI.UIPanel pnlDateSelector;
        private Sunny.UI.UIIntegerUpDown nudWeekNumber;
        private Sunny.UI.UILabel lblWeekYear;
        private Sunny.UI.UILabel lblWeekText;
        private Sunny.UI.UILabel lblDayText;
        private Sunny.UI.UIButton btnQuery;
        private Sunny.UI.UIPanel pnlPeriodType;
        private Sunny.UI.UIRadioButton rbYear;
        private Sunny.UI.UIRadioButton rbMonth;
        private Sunny.UI.UIRadioButton rbWeek;
        private Sunny.UI.UIRadioButton rbDay;
        private Sunny.UI.UILabel uiLabel3;
        private Sunny.UI.UIPanel pnlMain;
        private Sunny.UI.UIPanel pnlDataTable;
        private Sunny.UI.UIDataGridView dgvStatistics;
        private Sunny.UI.UILabel lblDataTableTitle;
        private LiveChartsCore.SkiaSharpView.WinForms.CartesianChart trendChart;
        private Sunny.UI.UIDatePicker dtpYear;
        private Sunny.UI.UIDatePicker dtpMonth;
        private Sunny.UI.UIDatePicker dtpEndTime;
        private Sunny.UI.UIDatePicker dtpStartTime;
    }
}
