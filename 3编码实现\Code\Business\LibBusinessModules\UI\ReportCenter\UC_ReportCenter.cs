using LibBaseModules.Helper;
using LibBusinessModules.DB;
using LibBusinessModules.DB.Models.PC;
using LibBusinessModules.Fault.Config;
using LibBusinessModules.ReportCenter.Config;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using Axis = LiveChartsCore.SkiaSharpView.Axis;

namespace LibBusinessModules.ReportCenter.UI
{
    /// <summary>
    /// 设备趋势统计界面
    /// </summary>
    public partial class UC_ReportCenter : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 当前选择的时间周期类型
        /// </summary>
        private TimePeriodType _currentPeriodType = TimePeriodType.Day;

        /// <summary>
        /// 查询起始时间
        /// </summary>
        private DateTime _startTime;

        /// <summary>
        /// 查询结束时间
        /// </summary>
        private DateTime _endTime;

        /// <summary>
        /// 界面是否初始化完成
        /// </summary>
        private bool _hasInit = false;

        /// <summary>
        /// 统计结果数据
        /// </summary>
        private List<DeviceTrendStatisticsItem> _statisticsData;

        #endregion

        #region 构造函数

        public UC_ReportCenter()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件处理

        private void UC_DeviceTrendStatistics_Load(object sender, EventArgs e)
        {
            if (!_hasInit)
            {
                _hasInit = true;
                InitializeDateTimePickers();
                InitializeChartSettings();
                UpdateDateSelectorVisibility();
                UpdateCurrentPeriodLabel();
            }
        }

        /// <summary>
        /// 时间周期选择变更事件
        /// </summary>
        private void rbPeriodType_CheckedChanged(object sender, EventArgs e)
        {
            var radioButton = sender as UIRadioButton;
            if (radioButton != null && radioButton.Checked)
            {
                if (radioButton == rbYear)
                    _currentPeriodType = TimePeriodType.Year;
                else if (radioButton == rbMonth)
                    _currentPeriodType = TimePeriodType.Month;
                else if (radioButton == rbWeek)
                    _currentPeriodType = TimePeriodType.Week;
                else if (radioButton == rbDay)
                    _currentPeriodType = TimePeriodType.Day;

                UpdateDateSelectorVisibility();
                UpdateCurrentPeriodLabel();
            }
        }

        /// <summary>
        /// 查询按钮点击事件
        /// </summary>
        private void btnQuery_Click(object sender, EventArgs e)
        {
            try
            {
                Enabled = false;
                
                // 根据当前周期类型设置查询时间范围
                SetQueryTimeRange();
                
                if (_startTime >= _endTime)
                {
                    throw new Exception("起始时间不能大于结束时间！");
                }

                QueryAndDisplayResults();
            }
            catch (Exception ex)
            {
                UIMessageBox.ShowError("查询数据错误:" + ex.Message);
            }
            finally
            {
                Enabled = true;
            }
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void btnExportPNG_Click(object sender, EventArgs e)
        {
            try
            {
                Enabled = false;
                if (_statisticsData == null || _statisticsData.Count == 0)
                {
                    throw new Exception("无数据可导出！");
                }

                // 导出时弹窗提示选取导出目录，及文件名称
                string filePath = Application.StartupPath + "\\export\\";
                if (!Directory.Exists(filePath))
                {
                    Directory.CreateDirectory(filePath);
                }

                saveFileDialog.InitialDirectory = filePath;
                var periodText = GetPeriodTypeText(_currentPeriodType);
                saveFileDialog.FileName = $"{filePath}设备趋势统计_{periodText}_{_startTime:yyyy-MM-dd}_{_endTime:yyyy-MM-dd}.png";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    // 显示等待界面
                    UIFormServiceHelper.ShowWaitForm(ParentForm, "正在导出图片，请稍候...");

                    // 使用LiveCharts的导出功能
                    ExportChartToImage(saveFileDialog.FileName);

                    // 隐藏等待界面
                    UIFormServiceHelper.HideWaitForm(ParentForm);

                    if (UIMessageBox.ShowAsk("导出成功！是否定位到文件所在位置？"))
                    {
                        var psi = new ProcessStartInfo("Explorer.exe")
                        {
                            Arguments = "/e,/select," + saveFileDialog.FileName
                        };
                        // 打开导出文件所在位置
                        Process.Start(psi);
                    }
                }
            }
            catch (Exception ex)
            {
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(ParentForm);
                UIMessageBox.ShowError($"导出失败：{ex.Message}");
            }
            finally
            {
                Enabled = true;
                // 隐藏等待界面
                UIFormServiceHelper.HideWaitForm(ParentForm);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化图表设置，配置中文字体支持
        /// </summary>
        private void InitializeChartSettings()
        {
            try
            {
                // 设置LiveCharts全局字体
                LiveCharts.Configure(config =>
                    config.HasGlobalSKTypeface(SKTypeface.FromFamilyName("Microsoft YaHei"))
                );

                // 设置图表标题
                trendChart.Title = new LiveChartsCore.SkiaSharpView.VisualElements.LabelVisual
                {
                    Text = "设备数量趋势统计",
                    TextSize = 16,
                    Paint = new SolidColorPaint(SKColors.Black)
                    {
                        SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                    }
                };
            }
            catch (Exception ex)
            {
                // 如果字体设置失败，记录错误但不影响程序运行
                UINotifierHelper.ShowNotifier($"图表初始化失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 初始化DateTimePicker控件的值
        /// </summary>
        private void InitializeDateTimePickers()
        {
            var today = DateTime.Today;
            
            // 日模式：默认最近30天
            dtpStartTime.Value = today.AddDays(-30);
            dtpEndTime.Value = today;
            
            // 年模式：当前年
            dtpYear.Value = new DateTime(today.Year, 1, 1);
            
            // 月模式：当前月
            dtpMonth.Value = new DateTime(today.Year, today.Month, 1);
            
            // 周模式：当前周的开始日期
            var startOfWeek = today.AddDays(-(int)today.DayOfWeek);
            dtpWeek.Value = startOfWeek;
        }

        /// <summary>
        /// 更新日期选择器的可见性
        /// </summary>
        private void UpdateDateSelectorVisibility()
        {
            // 隐藏所有日期选择器
            dtpYear.Visible = false;
            dtpMonth.Visible = false;
            dtpWeek.Visible = false;
            dtpStartTime.Visible = false;
            dtpEndTime.Visible = false;
            uiLabel1.Visible = false;

            // 根据当前周期类型显示对应的日期选择器
            switch (_currentPeriodType)
            {
                case TimePeriodType.Year:
                    dtpYear.Visible = true;
                    break;
                case TimePeriodType.Month:
                    dtpMonth.Visible = true;
                    break;
                case TimePeriodType.Week:
                    dtpWeek.Visible = true;
                    break;
                case TimePeriodType.Day:
                    dtpStartTime.Visible = true;
                    dtpEndTime.Visible = true;
                    uiLabel1.Visible = true;
                    break;
            }
        }

        /// <summary>
        /// 更新当前统计周期标签
        /// </summary>
        private void UpdateCurrentPeriodLabel()
        {
            string periodText = "";
            
            switch (_currentPeriodType)
            {
                case TimePeriodType.Year:
                    periodText = $"{dtpYear.Value.Year}年";
                    break;
                case TimePeriodType.Month:
                    periodText = $"{dtpMonth.Value:yyyy年MM月}";
                    break;
                case TimePeriodType.Week:
                    var weekStart = dtpWeek.Value;
                    var weekEnd = weekStart.AddDays(6);
                    var weekOfYear = GetWeekOfYear(weekStart);
                    periodText = $"{weekStart.Year}年第{weekOfYear}周 ({weekStart:MM月dd日}-{weekEnd:MM月dd日})";
                    break;
                case TimePeriodType.Day:
                    periodText = $"{dtpStartTime.Value:yyyy年MM月dd日} 至 {dtpEndTime.Value:yyyy年MM月dd日}";
                    break;
            }
            
            lblCurrentPeriod.Text = $"当前统计周期：{periodText}";
        }

        /// <summary>
        /// 获取周期类型的文本描述
        /// </summary>
        private string GetPeriodTypeText(TimePeriodType periodType)
        {
            switch (periodType)
            {
                case TimePeriodType.Year: return "年统计";
                case TimePeriodType.Month: return "月统计";
                case TimePeriodType.Week: return "周统计";
                case TimePeriodType.Day: return "日统计";
                default: return "统计";
            }
        }

        /// <summary>
        /// 获取指定日期是一年中的第几周
        /// </summary>
        private int GetWeekOfYear(DateTime date)
        {
            var culture = CultureInfo.CurrentCulture;
            var calendar = culture.Calendar;
            return calendar.GetWeekOfYear(date, culture.DateTimeFormat.CalendarWeekRule, culture.DateTimeFormat.FirstDayOfWeek);
        }

        /// <summary>
        /// 根据当前周期类型设置查询时间范围
        /// </summary>
        private void SetQueryTimeRange()
        {
            switch (_currentPeriodType)
            {
                case TimePeriodType.Year:
                    var year = dtpYear.Value.Year;
                    _startTime = new DateTime(year, 1, 1);
                    _endTime = new DateTime(year, 12, 31, 23, 59, 59);
                    break;
                case TimePeriodType.Month:
                    var monthStart = new DateTime(dtpMonth.Value.Year, dtpMonth.Value.Month, 1);
                    _startTime = monthStart;
                    _endTime = monthStart.AddMonths(1).AddSeconds(-1);
                    break;
                case TimePeriodType.Week:
                    var weekStart = dtpWeek.Value;
                    _startTime = weekStart;
                    _endTime = weekStart.AddDays(7).AddSeconds(-1);
                    break;
                case TimePeriodType.Day:
                    _startTime = dtpStartTime.Value.Date;
                    _endTime = dtpEndTime.Value.Date.AddDays(1).AddSeconds(-1);
                    break;
            }
        }

        /// <summary>
        /// 查询并显示结果
        /// </summary>
        private void QueryAndDisplayResults()
        {
            try
            {
                UIFormServiceHelper.ShowWaitForm(this.ParentForm, "数据统计中，请稍候...");

                // 计算统计数据
                CalculateDeviceTrendStatistics();

                // 显示统计结果
                DisplayTrendChart();

                // 更新当前周期标签
                UpdateCurrentPeriodLabel();
            }
            catch (Exception ex)
            {
                throw new Exception($"统计计算出错，{ex.Message}");
            }
            finally
            {
                // 隐藏进度条界面
                UIFormServiceHelper.HideWaitForm(this.ParentForm);
            }
        }

        /// <summary>
        /// 计算设备趋势统计数据
        /// </summary>
        private void CalculateDeviceTrendStatistics()
        {
            _statisticsData = new List<DeviceTrendStatisticsItem>();

            // 获取指定时间段内的设备记录
            var deviceRecords = DBHelper.GetPCDBContext().Queryable<DeviceInfo>()
                     .Where(data => data.TestTime >= _startTime && data.TestTime <= _endTime)
                     .ToList();

            if (deviceRecords.Count == 0)
            {
                throw new Exception("指定时间段内没有设备数据！");
            }

            // 生成时间点列表
            var timePoints = GenerateTimePoints();

            // 获取所有设备类型
            var deviceTypes = deviceRecords.Select(d => d.GetDeviceType()).Distinct().ToList();
            deviceTypes.Add("总数量"); // 添加总数量统计

            // 为每个时间点和设备类型计算数量
            foreach (var timePoint in timePoints)
            {
                var timeStart = timePoint;
                var timeEnd = GetTimePointEnd(timePoint);

                // 获取该时间段内的设备记录
                var periodDevices = deviceRecords.Where(d => d.TestTime >= timeStart && d.TestTime < timeEnd).ToList();

                foreach (var deviceType in deviceTypes)
                {
                    int count;
                    bool isTotal = deviceType == "总数量";

                    if (isTotal)
                    {
                        count = periodDevices.Count;
                    }
                    else
                    {
                        count = periodDevices.Count(d => d.GetDeviceType() == deviceType);
                    }

                    _statisticsData.Add(new DeviceTrendStatisticsItem
                    {
                        StatisticsTime = timePoint,
                        TimeLabel = GetTimeLabel(timePoint),
                        DeviceType = deviceType,
                        DeviceCount = count,
                        IsTotal = isTotal
                    });
                }
            }
        }

        /// <summary>
        /// 生成时间点列表
        /// </summary>
        private List<DateTime> GenerateTimePoints()
        {
            var timePoints = new List<DateTime>();

            switch (_currentPeriodType)
            {
                case TimePeriodType.Year:
                    // 按月统计
                    for (int month = 1; month <= 12; month++)
                    {
                        timePoints.Add(new DateTime(_startTime.Year, month, 1));
                    }
                    break;
                case TimePeriodType.Month:
                    // 按日统计
                    var current = _startTime.Date;
                    while (current <= _endTime.Date)
                    {
                        timePoints.Add(current);
                        current = current.AddDays(1);
                    }
                    break;
                case TimePeriodType.Week:
                    // 按日统计
                    current = _startTime.Date;
                    while (current <= _endTime.Date)
                    {
                        timePoints.Add(current);
                        current = current.AddDays(1);
                    }
                    break;
                case TimePeriodType.Day:
                    // 按日统计
                    current = _startTime.Date;
                    while (current <= _endTime.Date)
                    {
                        timePoints.Add(current);
                        current = current.AddDays(1);
                    }
                    break;
            }

            return timePoints;
        }

        /// <summary>
        /// 获取时间点的结束时间
        /// </summary>
        private DateTime GetTimePointEnd(DateTime timePoint)
        {
            switch (_currentPeriodType)
            {
                case TimePeriodType.Year:
                    // 月结束时间
                    return timePoint.AddMonths(1);
                case TimePeriodType.Month:
                case TimePeriodType.Week:
                case TimePeriodType.Day:
                    // 日结束时间
                    return timePoint.AddDays(1);
                default:
                    return timePoint.AddDays(1);
            }
        }

        /// <summary>
        /// 获取时间标签
        /// </summary>
        private string GetTimeLabel(DateTime timePoint)
        {
            switch (_currentPeriodType)
            {
                case TimePeriodType.Year:
                    return $"{timePoint.Month}月";
                case TimePeriodType.Month:
                case TimePeriodType.Week:
                case TimePeriodType.Day:
                    return timePoint.ToString("MM-dd");
                default:
                    return timePoint.ToString("MM-dd");
            }
        }

        /// <summary>
        /// 显示趋势图表
        /// </summary>
        private void DisplayTrendChart()
        {
            if (_statisticsData == null || _statisticsData.Count == 0)
            {
                throw new Exception("没有数据可显示！");
            }

            try
            {
                // 获取所有设备类型
                var deviceTypes = _statisticsData.Select(x => x.DeviceType).Distinct().ToList();
                var timeLabels = _statisticsData.Select(x => x.TimeLabel).Distinct().OrderBy(x => x).ToArray();

                // 创建系列集合
                var seriesList = new List<ISeries>();

                // 为每个设备类型创建一个折线系列
                foreach (var deviceType in deviceTypes)
                {
                    var typeData = _statisticsData.Where(x => x.DeviceType == deviceType)
                                                 .OrderBy(x => x.StatisticsTime)
                                                 .ToList();

                    var values = new List<double>();
                    foreach (var label in timeLabels)
                    {
                        var item = typeData.FirstOrDefault(x => x.TimeLabel == label);
                        values.Add(item?.DeviceCount ?? 0);
                    }

                    // 为总数量使用不同的颜色和线型
                    var series = new LiveChartsCore.SkiaSharpView.LineSeries<double>
                    {
                        Values = values,
                        Name = deviceType,
                        Fill = null, // 不填充
                        GeometrySize = 8,
                        LineSmoothness = 0, // 直线连接
                        Stroke = deviceType == "总数量" ?
                            new SolidColorPaint(SKColors.Red) { StrokeThickness = 3 } :
                            new SolidColorPaint(GetSeriesColor(deviceType)) { StrokeThickness = 2 },
                        GeometryStroke = deviceType == "总数量" ?
                            new SolidColorPaint(SKColors.Red) { StrokeThickness = 2 } :
                            new SolidColorPaint(GetSeriesColor(deviceType)) { StrokeThickness = 1 },
                        GeometryFill = deviceType == "总数量" ?
                            new SolidColorPaint(SKColors.Red) :
                            new SolidColorPaint(GetSeriesColor(deviceType))
                    };

                    seriesList.Add(series);
                }

                // 设置图表
                trendChart.Series = seriesList;

                // 设置X轴
                trendChart.XAxes = new Axis[]
                {
                    new Axis
                    {
                        Labels = timeLabels,
                        LabelsRotation = timeLabels.Length > 10 ? -45 : 0,
                        TextSize = 12,
                        LabelsPaint = new SolidColorPaint(SKColors.Black)
                        {
                            SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                        },
                        SeparatorsPaint = new SolidColorPaint(SKColors.LightGray)
                    }
                };

                // 设置Y轴
                trendChart.YAxes = new Axis[]
                {
                    new Axis
                    {
                        Name = "设备数量",
                        TextSize = 12,
                        LabelsPaint = new SolidColorPaint(SKColors.Black)
                        {
                            SKTypeface = SKTypeface.FromFamilyName("Microsoft YaHei")
                        },
                        SeparatorsPaint = new SolidColorPaint(SKColors.LightGray),
                        MinLimit = 0
                    }
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"显示图表失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取系列颜色
        /// </summary>
        private SKColor GetSeriesColor(string deviceType)
        {
            // 为不同设备类型分配不同颜色
            var colors = new SKColor[]
            {
                SKColors.Blue,
                SKColors.Green,
                SKColors.Orange,
                SKColors.Purple,
                SKColors.Brown,
                SKColors.Pink,
                SKColors.Gray,
                SKColors.Olive,
                SKColors.Navy,
                SKColors.Teal
            };

            var hash = deviceType.GetHashCode();
            var index = Math.Abs(hash) % colors.Length;
            return colors[index];
        }

        /// <summary>
        /// 导出图表为图片
        /// </summary>
        private void ExportChartToImage(string fileName)
        {
            try
            {
                // 使用LiveCharts的导出功能
                var skImageInfo = new SkiaSharp.SKImageInfo(1200, 800);
                using (var surface = SkiaSharp.SKSurface.Create(skImageInfo))
                {
                    var canvas = surface.Canvas;
                    canvas.Clear(SKColors.White);

                    // 绘制图表标题
                    var titlePaint = new SkiaSharp.SKPaint
                    {
                        Color = SKColors.Black,
                        TextSize = 24,
                        Typeface = SKTypeface.FromFamilyName("Microsoft YaHei"),
                        IsAntialias = true
                    };

                    var periodText = GetPeriodTypeText(_currentPeriodType);
                    var title = $"设备数量趋势统计 - {periodText}";
                    var titleBounds = new SkiaSharp.SKRect();
                    titlePaint.MeasureText(title, ref titleBounds);
                    var titleX = (skImageInfo.Width - titleBounds.Width) / 2;
                    canvas.DrawText(title, titleX, 40, titlePaint);

                    // 绘制时间范围信息
                    var infoPaint = new SkiaSharp.SKPaint
                    {
                        Color = SKColors.Gray,
                        TextSize = 16,
                        Typeface = SKTypeface.FromFamilyName("Microsoft YaHei"),
                        IsAntialias = true
                    };

                    var timeInfo = lblCurrentPeriod.Text;
                    canvas.DrawText(timeInfo, 20, 70, infoPaint);

                    // 绘制图表内容（这里简化处理，实际应该使用LiveCharts的导出API）
                    // 由于LiveCharts的导出API比较复杂，这里提供一个基础实现
                    // 在实际项目中，建议使用LiveCharts提供的官方导出方法

                    using (var image = surface.Snapshot())
                    using (var data = image.Encode(SkiaSharp.SKEncodedImageFormat.Png, 100))
                    using (var stream = File.OpenWrite(fileName))
                    {
                        data.SaveTo(stream);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"导出图片失败：{ex.Message}");
            }
        }

        #endregion
    }
}
